# 工作流程的名称
name: Release

# 定义触发此工作流程的事件
on:
  # 当有代码推送到仓库时触发
  push:
    # 只有推送到 "main" 分支时才触发
    branch: main
    # 只有推送了以 'v' 开头的 tag (标签) 时才触发
    tags:
      - 'v*'

# 定义此工作流程要执行的任务
jobs:
  # 任务的唯一ID，名为 "release"
  release:
    # "permissions" 定义了授予工作流程中 GITHUB_TOKEN 的权限
    permissions:
      # "contents: write" 是必需的权限，允许工作流程创建或修改仓库内容，例如发布 Release
      contents: write
    # 指定任务运行在 GitHub 托管的最新版 Ubuntu 虚拟机上
    runs-on: ubuntu-latest
    # "steps" 定义了此任务中要执行的一系列步骤
    steps:
      # 第一步：检出 (下载) 你的代码到虚拟机中
      - uses: actions/checkout@v4
        with:
          # "fetch-depth: 0" 表示获取所有历史的 git 提交记录。
          # 这对于自动生成更新日志 (changelog) 至关重要。
          fetch-depth: 0

      # 第二步：安装并设置 Node.js 环境
      - uses: actions/setup-node@v4
        with:
          # 指定安装最新版的 LTS (长期支持) Node.js
          node-version: lts/*

      # 第三步：运行命令来生成并发布 Release
      # "npx changelogithub" 是一个命令，它会执行一个 npm 包 (changelogithub)。
      # 这个工具会自动分析 git 提交，生成发布说明，并创建一个新的 GitHub Release。
      - run: npx changelogithub
        # "env" 为这个命令设置环境变量
        env:
          # 将 GitHub 自动提供的 GITHUB_TOKEN 传递给命令。
          # "changelogithub" 工具需要这个令牌 (Token) 来获得向你的仓库发布 Release 的权限。
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}