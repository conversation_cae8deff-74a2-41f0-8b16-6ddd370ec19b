// 从 Node.js 内置模块中导入同步写入文件的函数
import { writeFileSync } from "node:fs"
// 从 Node.js 内置模块中导入拼接路径的函数
import { join } from "node:path"
// 从第三方库 @napi-rs/pinyin 中导入 pinyin 函数，用于将汉字转换为拼音
import { pinyin } from "@napi-rs/pinyin"
// 导入第三方日志库，用于在控制台输出带样式的日志信息
import { consola } from "consola"
// 导入预设的项目根目录路径
import { projectDir } from "../shared/dir"
// 导入用于生成源数据的函数
import { genSources } from "../shared/pre-sources"

// 调用 genSources() 函数获取原始的数据源对象
const sources = genSources()

// --- 第一个主要任务：生成 pinyin.json ---
try {
  // 使用 Object.fromEntries 将处理后的键值对数组转换回对象
  const pinyinMap = Object.fromEntries(
    // 使用 Object.entries 将 sources 对象转换为 [key, value] 形式的数组
    Object.entries(sources)
      // 过滤掉那些包含 'redirect' 属性的源，因为它们是重定向项，不需要生成拼音
      .filter(([, v]) => !v.redirect)
      // 遍历过滤后的源数组，为每一个源生成一个 [id, pinyinString] 的新键值对
      .map(([k, v]) => {
        // 确定要转换成拼音的字符串：如果 v.title 存在，则拼接 v.name 和 v.title，否则只用 v.name
        const stringToConvert = v.title ? `${v.name}-${v.title}` : v.name
        // 调用 pinyin 函数将字符串转为拼音数组，然后用 .join("") 合并成一个无缝的拼音字符串
        const pinyinString = pinyin(stringToConvert).join("")
        // 返回一个包含原始 key 和生成的拼音字符串的新数组
        return [k, pinyinString]
      }),
  )

  // 将生成的 pinyinMap 对象写入到 ./shared/pinyin.json 文件中
  // JSON.stringify 的第三个参数 2 表示使用 2个空格进行格式化，使 JSON 文件更易读
  writeFileSync(join(projectDir, "./shared/pinyin.json"), JSON.stringify(pinyinMap, undefined, 2))
  // 如果成功，输出提示信息
  consola.info("Generated pinyin.json")
} catch {
  // 如果在 try 块中发生任何错误，则捕获并输出错误信息
  consola.error("Failed to generate pinyin.json")
}


// --- 第二个主要任务：生成 sources.json ---
try {
  // 将最开始获取的 sources 对象写入到 ./shared/sources.json 文件中
  // 这里的 Object.fromEntries(Object.entries(sources)) 是一种确保对象结构标准化的写法
  writeFileSync(join(projectDir, "./shared/sources.json"), JSON.stringify(Object.fromEntries(Object.entries(sources)), undefined, 2))
  // 如果成功，输出提示信息
  consola.info("Generated sources.json")
} catch {
  // 如果写入文件失败，则捕获并输出错误信息
  consola.error("Failed to generate sources.json")
}