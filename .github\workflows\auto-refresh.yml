# 工作流程的名称，会显示在 GitHub Actions 页面
name: Auto Refresh

# "on" 关键字定义了触发此工作流程的事件
on:
  # "schedule" 表示按计划定时触发
  schedule:
    # "cron" 表达式定义了具体的执行时间 (UTC 时间)
    # 每天的 0-17点、22点、23点，每隔20分钟执行一次
    - cron: '*/20 0-17,22,23 * * *'
  
  # "workflow_dispatch" 允许你从 GitHub 网站上手动触发此工作流程
  workflow_dispatch:

# "jobs" 定义了此工作流程要执行的一系列任务
jobs:
  # "release" 是这个任务的唯一ID
  release:
    # "runs-on" 指定任务运行的虚拟机环境
    # 这里使用的是 GitHub 提供的最新的 Ubuntu 系统
    runs-on: ubuntu-latest
    
    # "steps" 定义了此任务中要执行的一系列步骤
    steps:
      # 第一步：检出代码
      # 使用官方的 actions/checkout@v4 动作，将你的仓库代码下载到虚拟机中
      - uses: actions/checkout@v4
        with:
          # fetch-depth: 0 表示下载完整的 git 历史记录
          fetch-depth: 0

      # 第二步：设置 Node.js 环境
      # 使用官方的 actions/setup-node@v4 动作
      - uses: actions/setup-node@v4
        with:
          # 指定安装最新版的 LTS (长期支持) Node.js
          node-version: lts/*

      # 第三步：执行刷新脚本
      - name: Refresh # 为此步骤命名
        env:
          # 设置环境变量 JWT_TOKEN，其值从仓库的 Secrets 中获取
          JWT_TOKEN: ${{ secrets.JWT_TOKEN }}
        # "run" 后面是具体要执行的命令行指令
        # 使用 npx tsx 直接运行 TypeScript 脚本
        run: npx tsx ./scripts/refresh.ts