# 工作流程的名称，会显示在 GitHub Actions 页面
name: Publish Docker image

# "on" 关键字定义了触发此工作流程的事件
on:
  # 当有新的 tag (标签) 被推送到仓库时触发
  push:
    tags:
      # 只有以 'v' 开头的 tag 才会触发，例如 v1.0, v2.3.4
      - 'v*'
  
  # "workflow_dispatch" 允许你从 GitHub 网站上手动触发此工作流程
  workflow_dispatch:

# "jobs" 定义了此工作流程要执行的一系列任务
jobs:
  # "build-docker" 是这个任务的唯一ID
  build-docker:
    # 任务的显示名称
    name: Push Docker image to multiple registries
    # "runs-on" 指定任务运行的虚拟机环境，这里使用最新的 Ubuntu 系统
    runs-on: ubuntu-latest
    
    # "permissions" 定义了授予工作流程中 GITHUB_TOKEN 的权限
    permissions:
      # "packages: write" 允许工作流程向 GitHub Packages (包括 GHCR) 推送内容
      packages: write
      # "contents: read" 允许工作流程读取仓库的内容 (例如 checkout 代码)
      contents: read
      
    # "steps" 定义了此任务中要执行的一系列步骤
    steps:
      # 第一步：检出代码
      - name: Check out the repo
        # 使用官方的 actions/checkout@v4 动作，将你的仓库代码下载到虚拟机中
        uses: actions/checkout@v4

      # 第二步：设置 QEMU
      # QEMU 是一个模拟器，结合 Docker Buildx 可以实现跨平台构建镜像
      # 例如，在 x86 架构的机器上构建出 ARM 架构的镜像
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      # 第三步：设置 Docker Buildx
      # Buildx 是一个 Docker CLI 插件，它扩展了 docker build 命令，
      # 使其能够使用 Moby BuildKit 引擎的全部功能，支持多平台构建、缓存等高级特性
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # 第四步：登录到容器镜像仓库
      - name: Log in to the Container registry
        # 使用官方的 docker/login-action@v3 动作
        uses: docker/login-action@v3
        with:
          # 指定要登录的镜像仓库地址，ghcr.io 是 GitHub Container Registry
          registry: ghcr.io
          # 使用触发工作流程的用户的用户名
          username: ${{ github.actor }}
          # 使用 GitHub 自动生成的 GITHUB_TOKEN 作为密码进行认证
          # 这个 token 的权限由上面的 "permissions" 部分定义
          password: ${{ secrets.GITHUB_TOKEN }}

      # 第五步：为 Docker 提取元数据 (标签、标注)
      - name: Extract metadata (tags, labels) for Docker
        # 给这个步骤设置一个ID "meta"，方便后续步骤引用它的输出
        id: meta
        # 使用官方的 docker/metadata-action@v5 动作
        uses: docker/metadata-action@v5
        with:
          # 指定镜像的名称格式。ghcr.io/用户名/仓库名
          images: ghcr.io/${{ github.repository }}
          # 这个动作会自动根据 Git 的 tag、分支、提交 SHA 等信息，生成合适的 Docker 镜像标签

      # 第六步：构建并推送 Docker 镜像
      - name: Build and push
        # 使用官方的 docker/build-push-action@v5 动作
        uses: docker/build-push-action@v5
        with:
          # "context: ." 指定 Docker 构建的上下文路径为当前目录
          context: .
          # "file: ./Dockerfile" 指定 Dockerfile 文件的路径
          file: ./Dockerfile
          # "platforms" 指定要为哪些平台构建镜像
          platforms: |
            linux/amd64
            linux/arm64
          # "push: true" 表示在构建成功后，将镜像推送到远程仓库
          push: true
          # "tags" 设置镜像的标签，这里引用了第五步 "meta" 的输出结果
          tags: ${{ steps.meta.outputs.tags }}
          # "labels" 设置镜像的标注，同样引用了第五步 "meta" 的输出结果
          labels: ${{ steps.meta.outputs.labels }}