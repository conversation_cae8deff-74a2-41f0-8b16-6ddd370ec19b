# 指定 docker-compose 文件格式的版本，'3' 是一个常用且稳定的版本。
version: '3'

# "services" 关键字用来定义应用包含的各个服务（容器）。
services:
  # "ivdnewsletter" 是我们定义的服务名称。
  ivdnewsletter:
    # "build: ." 指示 Docker Compose 使用当前目录下的 Dockerfile 来构建这个服务的镜像。
    build: .
    # "ports" 用于设置端口映射，将主机端口连接到容器端口。
    ports:
      # - '主机端口:容器端口'
      # 这行表示将你的电脑（主机）的 4444 端口映射到容器内部的 4444 端口。
      # 这样，你就可以通过访问 localhost:4444 来访问容器内的应用。
      - '4444:4444'
    # "volumes" 用于数据持久化，将主机上的目录或 Docker 管理的卷挂载到容器内部。
    volumes:
      # - '卷名称:容器内路径'
      # 这行表示将一个名为 "ivdnewsletter_data" 的 Docker 卷挂载到容器的 /usr/app/.data 目录。
      # 这样做可以确保容器被删除重建后，/usr/app/.data 目录下的数据不会丢失。
      - ivdnewsletter_data:/usr/app/.data
    # "environment" 用于设置容器内部的环境变量。
    environment:
      # 设置 Google OAuth 客户端 ID，这里留空，通常需要在本地运行时手动填入或通过 .env 文件提供。
      - G_CLIENT_ID=
      # 设置 Google OAuth 客户端密钥。
      - G_CLIENT_SECRET=
      # 设置用于签发和验证 JWT (JSON Web Token) 的密钥。
      - JWT_SECRET=
      # 一个自定义的环境变量，"true" 可能表示在应用启动时需要初始化数据库表。
      - INIT_TABLE=true
      # 另一个自定义环境变量，"true" 表示启用应用的缓存功能。
      - ENABLE_CACHE=true

# "volumes" 顶层关键字用于定义在服务中可以被引用的命名卷。
volumes:
  # 定义一个名为 "ivdnewsletter_data" 的卷。
  ivdnewsletter_data:
    # 明确指定这个 Docker 卷在主机上的名称也为 "ivdnewsletter_data"。
    name: ivdnewsletter_data