import { defineSource } from "#/utils/source"

// For sources that have RSS feeds, we can use RSSHub or direct RSS
// For sources that don't have RSS feeds, we'll return placeholder data

export default defineSource({
  // Chinese regulatory sources
  "nmpa": async () => {
    // NMPA doesn't have RSS, return placeholder
    return [{
      id: "nmpa-1",
      title: "国家药监局医疗器械公告",
      url: "https://www.nmpa.gov.cn/ylqx/ylqxggtg/index.html",
      pubDate: Date.now(),
    }]
  },

  // International regulatory sources
  "fda": async () => {
    // FDA has RSS feeds, but for now return placeholder
    return [{
      id: "fda-1", 
      title: "FDA Medical Device News",
      url: "https://www.fda.gov/",
      pubDate: Date.now(),
    }]
  },

  "ema": async () => {
    return [{
      id: "ema-1",
      title: "EMA Medical Device Updates", 
      url: "https://www.ema.europa.eu/en/homepage",
      pubDate: Date.now(),
    }]
  },

  "pmda": async () => {
    return [{
      id: "pmda-1",
      title: "PMDA Medical Device Information",
      url: "https://www.pmda.go.jp/english/",
      pubDate: Date.now(),
    }]
  },

  "health-canada": async () => {
    return [{
      id: "health-canada-1",
      title: "Health Canada Medical Device Updates",
      url: "https://www.canada.ca/en/health-canada/services/drugs-health-products.html",
      pubDate: Date.now(),
    }]
  },

  "tga": async () => {
    return [{
      id: "tga-1",
      title: "TGA Medical Device Information",
      url: "https://www.tga.gov.au/",
      pubDate: Date.now(),
    }]
  },

  "mhra": async () => {
    return [{
      id: "mhra-1",
      title: "MHRA Medical Device Updates",
      url: "https://www.gov.uk/government/organisations/medicines-and-healthcare-products-regulatory-agency",
      pubDate: Date.now(),
    }]
  },

  "swissmedic": async () => {
    return [{
      id: "swissmedic-1",
      title: "Swissmedic Medical Device News",
      url: "https://www.swissmedic.ch/swissmedic/en/home.html",
      pubDate: Date.now(),
    }]
  },

  "anvisa": async () => {
    return [{
      id: "anvisa-1",
      title: "ANVISA Medical Device Updates",
      url: "https://www.gov.br/anvisa/pt-br/english",
      pubDate: Date.now(),
    }]
  },

  "mfds": async () => {
    return [{
      id: "mfds-1",
      title: "MFDS Medical Device Information",
      url: "https://www.mfds.go.kr/eng/index.do",
      pubDate: Date.now(),
    }]
  },

  "cdsco": async () => {
    return [{
      id: "cdsco-1",
      title: "CDSCO Medical Device Updates",
      url: "https://cdsco.gov.in/",
      pubDate: Date.now(),
    }]
  },

  // Industry organizations
  "advamed": async () => {
    return [{
      id: "advamed-1",
      title: "AdvaMed Industry News",
      url: "https://www.advamed.org/",
      pubDate: Date.now(),
    }]
  },

  "medtech-europe": async () => {
    return [{
      id: "medtech-europe-1",
      title: "MedTech Europe Updates",
      url: "https://www.medtecheurope.org/",
      pubDate: Date.now(),
    }]
  },

  "iso": async () => {
    return [{
      id: "iso-1",
      title: "ISO Medical Device Standards",
      url: "https://www.iso.org/home.html",
      pubDate: Date.now(),
    }]
  },

  // Industry media and news
  "readhub": async () => {
    return [{
      id: "readhub-1",
      title: "Readhub医疗行业资讯",
      url: "https://readhub.cn/medical",
      pubDate: Date.now(),
    }]
  },

  "vbdata": async () => {
    return [{
      id: "vbdata-1",
      title: "动脉网医疗健康资讯",
      url: "https://www.vbdata.cn/intelList",
      pubDate: Date.now(),
    }]
  },

  "mddionline": async () => {
    return [{
      id: "mddionline-1",
      title: "MDDI Online IVD News",
      url: "https://www.mddionline.com/medical-device-markets/ivd",
      pubDate: Date.now(),
    }]
  },

  "bioon": async () => {
    return [{
      id: "bioon-1",
      title: "生物谷医学资讯",
      url: "https://www.bioon.com/",
      pubDate: Date.now(),
    }]
  },

  "geneonline": async () => {
    return [{
      id: "geneonline-1",
      title: "基因线上生物技术资讯",
      url: "https://geneonline.news/",
      pubDate: Date.now(),
    }]
  },

  "ofweek-medical": async () => {
    return [{
      id: "ofweek-medical-1",
      title: "OFweek医疗科技资讯",
      url: "https://www.ofweek.com/medical/CATList-1111-8100-medical.html",
      pubDate: Date.now(),
    }]
  },

  "yaozh": async () => {
    return [{
      id: "yaozh-1",
      title: "药智网医药资讯",
      url: "https://qx.yaozh.com/",
      pubDate: Date.now(),
    }]
  },

  "pharnexcloud": async () => {
    return [{
      id: "pharnexcloud-1",
      title: "医药魔方产业数据",
      url: "https://www.pharnexcloud.com/",
      pubDate: Date.now(),
    }]
  },

  // Market research
  "databridge": async () => {
    return [{
      id: "databridge-1",
      title: "Data Bridge IVD Market Report",
      url: "https://www.databridgemarketresearch.com/zh/reports/global-in-vitro-diagnostics-ivd-market",
      pubDate: Date.now(),
    }]
  },

  "gminsights": async () => {
    return [{
      id: "gminsights-1",
      title: "GM Insights Biotechnology Market",
      url: "https://www.gminsights.com/zh/industry-analysis/biotechnology-market",
      pubDate: Date.now(),
    }]
  },

  "iqvia": async () => {
    return [{
      id: "iqvia-1",
      title: "IQVIA Healthcare Data Analysis",
      url: "https://www.iqvia.com/locations/china",
      pubDate: Date.now(),
    }]
  },

  "deloitte": async () => {
    return [{
      id: "deloitte-1",
      title: "德勤生命科学行业洞察",
      url: "https://www.deloitte.com/cn/zh/Industries/life-sciences-health-care/perspectives/lshc-publications.html",
      pubDate: Date.now(),
    }]
  },

  // Clinical trials
  "clinicaltrials": async () => {
    return [{
      id: "clinicaltrials-1",
      title: "ClinicalTrials.gov Updates",
      url: "https://clinicaltrials.gov/",
      pubDate: Date.now(),
    }]
  },

  "who-ictrp": async () => {
    return [{
      id: "who-ictrp-1",
      title: "WHO ICTRP Clinical Trials",
      url: "https://www.who.int/tools/clinical-trials-registry-platform",
      pubDate: Date.now(),
    }]
  },

  "chictr": async () => {
    return [{
      id: "chictr-1",
      title: "中国临床试验注册中心更新",
      url: "https://www.chictr.org.cn/",
      pubDate: Date.now(),
    }]
  },

  // Patent databases
  "wipo": async () => {
    return [{
      id: "wipo-1",
      title: "WIPO Patent Database Updates",
      url: "https://patentscope.wipo.int/",
      pubDate: Date.now(),
    }]
  },

  "espacenet": async () => {
    return [{
      id: "espacenet-1",
      title: "Espacenet Patent Database",
      url: "https://worldwide.espacenet.com/",
      pubDate: Date.now(),
    }]
  },
})
