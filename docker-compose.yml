# 指定 docker-compose 文件格式的版本，'3' 是一个常用且稳定的版本。
version: '3'

# "services" 关键字用来定义应用包含的各个服务（容器）。
services:
  # "ivdnewsletter" 是我们定义的服务名称。
  ivdnewsletter:
    # "image" 指定服务要使用的 Docker 镜像。
    # 这里直接从 GitHub Container Registry (ghcr.io) 拉取一个公开的镜像。
    # "ghcr.io/ourongxing/ivdnewsletter:latest" 表示使用 "ourongxing/ivdnewsletter" 仓库的 "latest" (最新) 标签的镜像。
    image: ghcr.io/ourongxing/ivdnewsletter:latest
    # "container_name" 为启动的容器指定一个固定的名称，方便管理。
    container_name: ivdnewsletter
    # "ports" 用于设置端口映射，将主机端口连接到容器端口。
    ports:
      # - '主机端口:容器端口'
      # 将主机的 4444 端口映射到容器的 4444 端口。
      - '4444:4444'
    # "volumes" 用于数据持久化。
    volumes:
      # 将一个名为 "ivdnewsletter_data" 的 Docker 卷挂载到容器的 /usr/app/.data 目录，
      # 以确保应用数据在容器重启或更新后不丢失。
      - ivdnewsletter_data:/usr/app/.data
    # "environment" 用于设置容器内部的环境变量。
    environment:
      # HOST=0.0.0.0 让容器内的应用监听所有可用的网络接口，而不仅仅是 localhost。
      - HOST=0.0.0.0
      # PORT=4444 告诉应用在容器内部应该监听哪个端口。
      - PORT=4444
      # 将 Node.js 环境设置为 "production"，这通常会开启性能优化并关闭调试信息。
      - NODE_ENV=production
      # 设置 Google OAuth 客户端 ID，留空，需要在部署时通过其他方式（如 .env 文件或直接修改）提供。
      - G_CLIENT_ID=
      # 设置 Google OAuth 客户端密钥。
      - G_CLIENT_SECRET=
      # 设置用于签发和验证 JWT 的密钥。
      - JWT_SECRET=
      # 自定义环境变量，"true" 可能表示在应用启动时需要初始化数据库表。
      - INIT_TABLE=true
      # 自定义环境变量，"true" 表示启用应用的缓存功能。
      - ENABLE_CACHE=true

# "volumes" 顶层关键字用于定义在服务中可以被引用的命名卷。
volumes:
  # 定义一个名为 "ivdnewsletter_data" 的卷。
  ivdnewsletter_data:
    # 明确指定这个 Docker 卷在主机上的名称也为 "ivdnewsletter_data"。
    name: ivdnewsletter_data