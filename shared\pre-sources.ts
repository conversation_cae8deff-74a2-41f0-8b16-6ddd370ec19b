import process from "node:process"
import { Interval } from "./consts"
import { typeSafeObjectFromEntries } from "./type.util"
import type { OriginSource, Source, SourceID } from "./types"

const Time = {
  Test: 1,
  Realtime: 2 * 60 * 1000,
  Fast: 5 * 60 * 1000,
  Default: Interval, // 10min
  Common: 30 * 60 * 1000,
  Slow: 60 * 60 * 1000,
}

export const originSources = {
  // 中国监管机构
  "nmpa": {
    name: "国家药监局",
    color: "red",
    column: "china",
    home: "https://www.nmpa.gov.cn/ylqx/ylqxggtg/index.html",
    desc: "中国国家药品监督管理局医疗器械公告",
    interval: Time.Common,
  },

  // 国际监管机构
  "fda": {
    name: "FDA",
    color: "blue",
    column: "world",
    home: "https://www.fda.gov/",
    desc: "美国食品药品监督管理局",
    interval: Time.Common,
  },
  "ema": {
    name: "EMA",
    color: "blue",
    column: "world",
    home: "https://www.ema.europa.eu/en/homepage",
    desc: "欧洲药品管理局",
    interval: Time.Common,
  },
  "pmda": {
    name: "PMDA",
    color: "blue",
    column: "world",
    home: "https://www.pmda.go.jp/english/",
    desc: "日本药品医疗器械局",
    interval: Time.Common,
  },
  "health-canada": {
    name: "Health Canada",
    color: "blue",
    column: "world",
    home: "https://www.canada.ca/en/health-canada/services/drugs-health-products.html",
    desc: "加拿大卫生部",
    interval: Time.Common,
  },
  "tga": {
    name: "TGA",
    color: "blue",
    column: "world",
    home: "https://www.tga.gov.au/",
    desc: "澳大利亚治疗用品管理局",
    interval: Time.Common,
  },
  "mhra": {
    name: "MHRA",
    color: "blue",
    column: "world",
    home: "https://www.gov.uk/government/organisations/medicines-and-healthcare-products-regulatory-agency",
    desc: "英国药品和医疗产品监管局",
    interval: Time.Common,
  },
  "swissmedic": {
    name: "Swissmedic",
    color: "blue",
    column: "world",
    home: "https://www.swissmedic.ch/swissmedic/en/home.html",
    desc: "瑞士药品监管局",
    interval: Time.Common,
  },
  "anvisa": {
    name: "ANVISA",
    color: "blue",
    column: "world",
    home: "https://www.gov.br/anvisa/pt-br/english",
    desc: "巴西国家卫生监督局",
    interval: Time.Common,
  },
  "mfds": {
    name: "MFDS",
    color: "blue",
    column: "world",
    home: "https://www.mfds.go.kr/eng/index.do",
    desc: "韩国食品药品安全部",
    interval: Time.Common,
  },
  "cdsco": {
    name: "CDSCO",
    color: "blue",
    column: "world",
    home: "https://cdsco.gov.in/",
    desc: "印度中央药品标准控制组织",
    interval: Time.Common,
  },

  // 行业组织
  "advamed": {
    name: "AdvaMed",
    color: "green",
    column: "world",
    home: "https://www.advamed.org/",
    desc: "美国先进医疗技术协会",
    interval: Time.Common,
  },
  "medtech-europe": {
    name: "MedTech Europe",
    color: "green",
    column: "world",
    home: "https://www.medtecheurope.org/",
    desc: "欧洲医疗技术协会",
    interval: Time.Common,
  },
  "iso": {
    name: "ISO",
    color: "green",
    column: "world",
    home: "https://www.iso.org/home.html",
    desc: "国际标准化组织",
    interval: Time.Common,
  },

  // 行业媒体和资讯
  "readhub": {
    name: "Readhub医疗",
    color: "orange",
    column: "china",
    home: "https://readhub.cn/medical",
    desc: "医疗行业资讯聚合",
    interval: Time.Default,
  },
  "vbdata": {
    name: "动脉网",
    color: "orange",
    column: "china",
    home: "https://www.vbdata.cn/intelList",
    desc: "医疗健康产业资讯",
    interval: Time.Default,
  },
  "mddionline": {
    name: "MDDI Online",
    color: "orange",
    column: "world",
    home: "https://www.mddionline.com/medical-device-markets/ivd",
    desc: "医疗器械设计与制造",
    interval: Time.Common,
  },
  "bioon": {
    name: "生物谷",
    color: "orange",
    column: "china",
    home: "https://www.bioon.com/",
    desc: "生物医学资讯平台",
    interval: Time.Default,
  },
  "geneonline": {
    name: "基因线上",
    color: "orange",
    column: "china",
    home: "https://geneonline.news/",
    desc: "基因与生物技术资讯",
    interval: Time.Default,
  },
  "ofweek-medical": {
    name: "OFweek医疗",
    color: "orange",
    column: "china",
    home: "https://www.ofweek.com/medical/CATList-1111-8100-medical.html",
    desc: "医疗科技资讯",
    interval: Time.Default,
  },
  "yaozh": {
    name: "药智网",
    color: "orange",
    column: "china",
    home: "https://qx.yaozh.com/",
    desc: "医药行业资讯",
    interval: Time.Default,
  },
  "pharnexcloud": {
    name: "医药魔方",
    color: "orange",
    column: "china",
    home: "https://www.pharnexcloud.com/",
    desc: "医药产业数据平台",
    interval: Time.Default,
  },

  // 市场研究
  "databridge": {
    name: "Data Bridge",
    color: "purple",
    column: "finance",
    home: "https://www.databridgemarketresearch.com/zh/reports/global-in-vitro-diagnostics-ivd-market",
    desc: "IVD市场研究报告",
    interval: Time.Slow,
  },
  "gminsights": {
    name: "GM Insights",
    color: "purple",
    column: "finance",
    home: "https://www.gminsights.com/zh/industry-analysis/biotechnology-market",
    desc: "生物技术市场分析",
    interval: Time.Slow,
  },
  "iqvia": {
    name: "IQVIA",
    color: "purple",
    column: "finance",
    home: "https://www.iqvia.com/locations/china",
    desc: "医疗健康数据分析",
    interval: Time.Slow,
  },
  "deloitte": {
    name: "德勤生命科学",
    color: "purple",
    column: "finance",
    home: "https://www.deloitte.com/cn/zh/Industries/life-sciences-health-care/perspectives/lshc-publications.html",
    desc: "生命科学行业洞察",
    interval: Time.Slow,
  },

  // 临床试验
  "clinicaltrials": {
    name: "ClinicalTrials.gov",
    color: "teal",
    column: "tech",
    home: "https://clinicaltrials.gov/",
    desc: "美国临床试验数据库",
    interval: Time.Common,
  },
  "who-ictrp": {
    name: "WHO ICTRP",
    color: "teal",
    column: "tech",
    home: "https://www.who.int/tools/clinical-trials-registry-platform",
    desc: "世界卫生组织临床试验注册平台",
    interval: Time.Common,
  },
  "chictr": {
    name: "中国临床试验注册中心",
    color: "teal",
    column: "tech",
    home: "https://www.chictr.org.cn/",
    desc: "中国临床试验注册中心",
    interval: Time.Common,
  },

  // 专利数据库
  "wipo": {
    name: "WIPO",
    color: "indigo",
    column: "tech",
    home: "https://patentscope.wipo.int/",
    desc: "世界知识产权组织专利数据库",
    interval: Time.Slow,
  },
  "espacenet": {
    name: "Espacenet",
    color: "indigo",
    column: "tech",
    home: "https://worldwide.espacenet.com/",
    desc: "欧洲专利局专利数据库",
    interval: Time.Slow,
  },
} as const satisfies Record<string, OriginSource>

export function genSources() {
  const _: [SourceID, Source][] = []

  Object.entries(originSources).forEach(([id, source]: [any, OriginSource]) => {
    const parent = {
      name: source.name,
      type: source.type,
      disable: source.disable,
      desc: source.desc,
      column: source.column,
      home: source.home,
      color: source.color ?? "primary",
      interval: source.interval ?? Time.Default,
    }
    if (source.sub && Object.keys(source.sub).length) {
      Object.entries(source.sub).forEach(([subId, subSource], i) => {
        if (i === 0) {
          _.push([
            id,
            {
              redirect: `${id}-${subId}`,
              ...parent,
              ...subSource,
            },
          ] as [any, Source])
        }
        _.push([`${id}-${subId}`, { ...parent, ...subSource }] as [
          any,
          Source,
        ])
      })
    } else {
      _.push([
        id,
        {
          title: source.title,
          ...parent,
        },
      ])
    }
  })

  return typeSafeObjectFromEntries(
    _.filter(([_, v]) => {
      if (v.disable === "cf" && process.env.CF_PAGES) {
        return false
      } else if (v.disable === true) {
        return false
      } else {
        return true
      }
    }),
  )
}
