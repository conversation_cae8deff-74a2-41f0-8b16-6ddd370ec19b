// 监听 "install" 事件，当 Service Worker 首次被浏览器安装时触发
self.addEventListener("install", (e) => {
  // 调用 self.skipWaiting() 会强制新的 Service Worker 在安装完成后立即进入 activate 状态，
  // 而不是等待现有 Service Worker 停止控制页面。
  self.skipWaiting()
})

// 监听 "activate" 事件，当 Service Worker 被激活并开始控制页面时触发
self.addEventListener("activate", (e) => {
  // 通过 self.registration.unregister() 来注销当前的 Service Worker。
  // 这会告诉浏览器在下次加载时不再使用它。
  self.registration
    .unregister()
    // 注销成功后，获取当前此 Service Worker 控制的所有客户端（例如打开的标签页）
    .then(() => self.clients.matchAll())
    // 成功获取到客户端列表后执行
    .then((clients) => {
      // 遍历所有客户端
      clients.forEach((client) => {
        // 如果客户端是一个窗口 (WindowClient)，则强制刷新该页面。
        // client.navigate(client.url) 的效果就是重新加载当前 URL。
        // 这是为了确保页面不再受到已被注销的 Service Worker 的影响。
        if (client instanceof WindowClient) client.navigate(client.url)
      })
      // 返回一个 resolved 的 Promise 以便继续链式调用
      return Promise.resolve()
    })
    // 页面都刷新后，开始清理缓存
    .then(() => {
      // 获取所有缓存的名称
      self.caches.keys().then((cacheNames) => {
        // 使用 Promise.all 等待所有删除操作完成
        Promise.all(
          // 遍历所有缓存名称，并为每一个都创建一个删除操作的 Promise
          cacheNames.map((cacheName) => {
            return self.caches.delete(cacheName)
          }),
        )
      })
    })
})