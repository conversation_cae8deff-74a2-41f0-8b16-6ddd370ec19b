//一个 Node.js 脚本，它的主要功能是批量下载网站图标 (favicon)。它会遍历一个预定义的网站列表 (originSources)，然后使用 DuckDuckGo 的图标服务去获取每个网站的图标，并将其保存到本地的 public/icons 目录下。
// 从 Node.js 内置模块中导入所需的功能
import fs from "node:fs" // fs: 文件系统模块，用于读写文件
import { fileURLToPath } from "node:url" // fileURLToPath: 用于将文件 URL 转换为路径字符串
import { join } from "node:path" // join: 用于拼接路径片段
import { Buffer } from "node:buffer" // Buffer: 用于处理二进制数据

// 导入第三方日志库，用于在控制台输出带样式的日志信息
import { consola } from "consola"
// 导入预定义的源数据，这可能是一个包含网站信息的对象
import { originSources } from "../shared/pre-sources"

// 获取当前项目的根目录路径
// import.meta.url 返回当前模块的 URL (例如 file:///path/to/project/src/script.js)
// new URL("..", import.meta.url) 解析出上一级目录的 URL
// fileURLToPath 将这个 URL 转换为平台相关的绝对路径
const projectDir = fileURLToPath(new URL("..", import.meta.url))
// 拼接出存放图标的目标文件夹路径 (例如 /path/to/project/public/icons)
const iconsDir = join(projectDir, "public", "icons")

/**
 * 异步下载图片并保存到本地的函数
 * @param url 图片的 URL 地址
 * @param outputPath 图片保存的本地完整路径
 * @param id 用于日志输出的标识符
 */
async function downloadImage(url: string, outputPath: string, id: string) {
  try {
    // 使用 fetch API 请求图片 URL
    const response = await fetch(url)
    // 检查响应是否成功 (HTTP 状态码在 200-299 之间)
    if (!response.ok) {
      // 如果请求失败，则抛出错误
      throw new Error(`${id}: could not fetch ${url}, status: ${response.status}`)
    }

    // 再次请求 URL (注意: 这里的 fetch 是重复的，可以优化为使用上面的 response)
    // .arrayBuffer() 将响应体解析为二进制的 ArrayBuffer
    const image = await (await fetch(url)).arrayBuffer()
    // 使用 fs.writeFileSync 将二进制数据同步写入到指定的文件路径
    // Buffer.from(image) 将 ArrayBuffer 转换为 Node.js 的 Buffer 对象
    fs.writeFileSync(outputPath, Buffer.from(image))
    // 如果成功，使用 consola 输出成功日志
    consola.success(`${id}: downloaded successfully.`)
  } catch (error) {
    // 如果过程中发生任何错误，使用 consola 输出错误日志
    consola.error(`${id}: error downloading the image. `, error)
  }
}

/**
 * 主执行函数
 */
async function main() {
  // 使用 Promise.all 来并发执行所有的下载任务
  await Promise.all(
    // Object.entries(originSources) 将来源对象转换为 [key, value] 数组
    // .map() 遍历这个数组，为每个网站源创建一个下载任务
    Object.entries(originSources).map(async ([id, source]) => {
      try {
        // 构造图标在本地的完整保存路径，文件名统一为 id.png
        const icon = join(iconsDir, `${id}.png`)
        // 检查本地是否已存在该图标文件
        if (fs.existsSync(icon)) {
          // 如果文件已存在，则跳过下载
          // consola.info(`${id}: icon exists. skip.`) // 这行是注释掉的，原本用于输出跳过信息
          return
        }
        // 如果来源信息中没有 home 字段 (主页URL)，则跳过
        if (!source.home) return
        
        // 准备调用 downloadImage 函数
        // 第一个参数是构造的 DuckDuckGo 图标服务 URL
        // source.home.replace(...) 部分是为了从主页 URL 中提取出域名
        // .replace(/^https?:\/\//, "") 移除 http:// 或 https://
        // .replace(/\/$/, "") 移除末尾的斜杠
        await downloadImage(`https://icons.duckduckgo.com/ip3/${source.home.replace(/^https?:\/\//, "").replace(/\/$/, "")}.ico`, icon, id)
      } catch (e) {
        // 捕获单个任务的异常，防止一个失败导致整个 Promise.all 中断
        consola.error(id, "\n", e)
      }
    }),
  )
}

// 调用主函数，开始执行脚本
main()